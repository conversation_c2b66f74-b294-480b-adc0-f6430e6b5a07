#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用純socket實現的Modbus TCP讀取程式
不依賴第三方庫，只使用Python標準庫
"""

import socket
import struct
import time
import sys

class ModbusTCPSocket:
    def __init__(self, host='***********', port=502):
        self.host = host
        self.port = port
        self.sock = None
        self.transaction_id = 0
        
    def connect(self):
        """連接PLC"""
        try:
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.settimeout(3)
            self.sock.connect((self.host, self.port))
            print(f"成功連接到PLC: {self.host}:{self.port}")
            return True
        except Exception as e:
            print(f"連接錯誤: {e}")
            return False
    
    def read_coils(self, start_address, count):
        """讀取線圈狀態"""
        try:
            # 構建Modbus TCP請求
            self.transaction_id += 1
            
            # Modbus TCP ADU格式
            # Transaction ID (2 bytes) + Protocol ID (2 bytes) + Length (2 bytes) + Unit ID (1 byte) + PDU
            transaction_id = self.transaction_id
            protocol_id = 0
            unit_id = 1
            
            # PDU: Function Code (1 byte) + Start Address (2 bytes) + Quantity (2 bytes)
            function_code = 1  # Read Coils
            pdu = struct.pack('>BHH', function_code, start_address, count)
            length = len(pdu) + 1  # PDU length + Unit ID
            
            # 完整的ADU
            adu = struct.pack('>HHHB', transaction_id, protocol_id, length, unit_id) + pdu
            
            # 發送請求
            self.sock.send(adu)
            
            # 接收響應
            response = self.sock.recv(1024)
            
            # 解析響應
            if len(response) < 9:
                print("響應太短")
                return None
                
            # 解析MBAP頭部
            resp_trans_id, resp_proto_id, resp_length, resp_unit_id = struct.unpack('>HHHB', response[:7])
            
            # 檢查響應
            if resp_trans_id != transaction_id:
                print("Transaction ID不匹配")
                return None
                
            # 解析PDU
            resp_func_code = response[7]
            if resp_func_code != function_code:
                print(f"功能碼錯誤: {resp_func_code}")
                return None
                
            byte_count = response[8]
            coil_data = response[9:9+byte_count]
            
            # 轉換為位列表
            coils = []
            for byte_val in coil_data:
                for bit in range(8):
                    if len(coils) < count:
                        coils.append(bool(byte_val & (1 << bit)))
                        
            return coils[:count]
            
        except Exception as e:
            print(f"讀取錯誤: {e}")
            return None
    
    def display_status(self, coils, start_address=100):
        """顯示狀態"""
        if coils is None:
            print("無法獲取狀態")
            return
            
        print("\n" + "="*50)
        print(f"匯川PLC M寄存器狀態 ({time.strftime('%Y-%m-%d %H:%M:%S')})")
        print("="*50)
        
        for i, coil in enumerate(coils):
            address = start_address + i
            status = "ON" if coil else "OFF"
            print(f"M{address:03d}: {status}")
        
        print("="*50)
    
    def close(self):
        """關閉連接"""
        if self.sock:
            self.sock.close()
            print("已關閉PLC連接")

def main():
    """主程式"""
    plc = ModbusTCPSocket()
    
    try:
        if not plc.connect():
            sys.exit(1)
        
        print("\n開始讀取M100~M120狀態...")
        print("按 Ctrl+C 停止程式")
        
        while True:
            coils = plc.read_coils(start_address=100, count=21)
            plc.display_status(coils, start_address=100)
            time.sleep(2)
            
    except KeyboardInterrupt:
        print("\n\n程式被用戶中斷")
    except Exception as e:
        print(f"程式執行錯誤: {e}")
    finally:
        plc.close()

if __name__ == "__main__":
    main()
