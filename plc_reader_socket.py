#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用純socket實現的Modbus TCP讀取程式
不依賴第三方庫，只使用Python標準庫
"""

import socket
import struct
import time
import sys

class ModbusTCPSocket:
    def __init__(self, host='***********', port=502):
        self.host = host
        self.port = port
        self.sock = None
        self.transaction_id = 0
        self.debug = True  # 添加調試模式
        
    def connect(self):
        """連接PLC"""
        try:
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.settimeout(10)  # 增加超時時間
            if self.debug:
                print(f"嘗試連接到PLC: {self.host}:{self.port}")
            self.sock.connect((self.host, self.port))
            print(f"成功連接到PLC: {self.host}:{self.port}")
            return True
        except socket.timeout:
            print(f"連接超時: {self.host}:{self.port} - 請檢查PLC是否在線")
            return False
        except ConnectionRefusedError:
            print(f"連接被拒絕: {self.host}:{self.port} - 請檢查PLC Modbus TCP服務是否啟用")
            return False
        except Exception as e:
            print(f"連接錯誤: {e}")
            return False
    
    def read_coils(self, start_address, count):
        """讀取線圈狀態"""
        try:
            # 構建Modbus TCP請求
            self.transaction_id += 1

            # Modbus TCP ADU格式
            # Transaction ID (2 bytes) + Protocol ID (2 bytes) + Length (2 bytes) + Unit ID (1 byte) + PDU
            transaction_id = self.transaction_id
            protocol_id = 0
            unit_id = 1

            # PDU: Function Code (1 byte) + Start Address (2 bytes) + Quantity (2 bytes)
            function_code = 1  # Read Coils
            # 注意：匯川PLC的M寄存器可能需要地址偏移
            # M100在Modbus中可能對應地址100或其他值，需要根據PLC配置調整
            modbus_address = start_address  # 先嘗試直接映射
            pdu = struct.pack('>BHH', function_code, modbus_address, count)
            length = len(pdu) + 1  # PDU length + Unit ID

            # 完整的ADU
            adu = struct.pack('>HHHB', transaction_id, protocol_id, length, unit_id) + pdu

            if self.debug:
                print(f"發送請求: 地址={modbus_address}, 數量={count}")
                print(f"ADU: {adu.hex()}")

            # 發送請求
            self.sock.send(adu)

            # 接收響應
            response = self.sock.recv(1024)

            if self.debug:
                print(f"收到響應: {response.hex()}")
            
            # 解析響應
            if len(response) < 9:
                print(f"響應太短: 長度={len(response)}")
                return None

            # 解析MBAP頭部
            resp_trans_id, resp_proto_id, resp_length, resp_unit_id = struct.unpack('>HHHB', response[:7])

            if self.debug:
                print(f"響應解析: trans_id={resp_trans_id}, proto_id={resp_proto_id}, length={resp_length}, unit_id={resp_unit_id}")

            # 檢查響應
            if resp_trans_id != transaction_id:
                print(f"Transaction ID不匹配: 期望={transaction_id}, 實際={resp_trans_id}")
                return None

            # 解析PDU
            resp_func_code = response[7]
            if resp_func_code != function_code:
                # 檢查是否為錯誤響應
                if resp_func_code == (function_code + 0x80):
                    error_code = response[8] if len(response) > 8 else 0
                    print(f"Modbus錯誤響應: 錯誤碼={error_code}")
                    return None
                print(f"功能碼錯誤: 期望={function_code}, 實際={resp_func_code}")
                return None

            byte_count = response[8]
            coil_data = response[9:9+byte_count]

            if self.debug:
                print(f"數據字節數: {byte_count}")
                print(f"線圈數據: {[hex(b) for b in coil_data]}")

            # 轉換為位列表
            coils = []
            for byte_val in coil_data:
                for bit in range(8):
                    if len(coils) < count:
                        coils.append(bool(byte_val & (1 << bit)))

            return coils[:count]
            
        except Exception as e:
            print(f"讀取錯誤: {e}")
            return None
    
    def test_m101_specifically(self):
        """專門測試M101的狀態"""
        print("\n" + "="*60)
        print("專門測試M101狀態")
        print("="*60)

        # 嘗試不同的地址映射方式
        test_addresses = [
            (101, "直接地址101"),
            (100, "從M100開始讀取"),
            (1, "只讀取1個線圈"),
            (0, "從地址0開始(如果M100對應Modbus地址0)")
        ]

        for addr, desc in test_addresses:
            print(f"\n測試: {desc} (Modbus地址: {addr})")
            try:
                if addr == 100:
                    # 讀取M100-M105，然後取M101
                    coils = self.read_coils(addr, 6)
                    if coils and len(coils) > 1:
                        m101_status = coils[1]  # M101是第二個
                        print(f"M101狀態: {'ON' if m101_status else 'OFF'}")
                        # 顯示周圍的狀態作為參考
                        for i, coil in enumerate(coils):
                            print(f"  M{100+i}: {'ON' if coil else 'OFF'}")
                    else:
                        print("讀取失敗")
                else:
                    # 直接讀取單個地址
                    coils = self.read_coils(addr, 1)
                    if coils:
                        print(f"地址{addr}狀態: {'ON' if coils[0] else 'OFF'}")
                    else:
                        print("讀取失敗")
            except Exception as e:
                print(f"測試失敗: {e}")

        print("="*60)

    def display_status(self, coils, start_address=100):
        """顯示狀態"""
        if coils is None:
            print("無法獲取狀態")
            return

        print("\n" + "="*50)
        print(f"匯川PLC M寄存器狀態 ({time.strftime('%Y-%m-%d %H:%M:%S')})")
        print("="*50)

        for i, coil in enumerate(coils):
            address = start_address + i
            status = "ON" if coil else "OFF"
            # 特別標記M101
            marker = " ← M101" if address == 101 else ""
            print(f"M{address:03d}: {status}{marker}")

        print("="*50)
    
    def close(self):
        """關閉連接"""
        if self.sock:
            self.sock.close()
            print("已關閉PLC連接")

def main():
    """主程式"""
    plc = ModbusTCPSocket()

    try:
        if not plc.connect():
            print("\n無法連接到PLC，請檢查:")
            print("1. PLC是否開機並連接到網路")
            print("2. IP地址是否正確 (當前: ***********)")
            print("3. Modbus TCP服務是否啟用")
            print("4. 防火牆設定")
            sys.exit(1)

        # 先進行M101專門測試
        plc.test_m101_specifically()

        print("\n開始讀取M100~M120狀態...")
        print("按 Ctrl+C 停止程式")

        while True:
            coils = plc.read_coils(start_address=100, count=21)
            plc.display_status(coils, start_address=100)
            time.sleep(2)

    except KeyboardInterrupt:
        print("\n\n程式被用戶中斷")
    except Exception as e:
        print(f"程式執行錯誤: {e}")
    finally:
        plc.close()

if __name__ == "__main__":
    main()
