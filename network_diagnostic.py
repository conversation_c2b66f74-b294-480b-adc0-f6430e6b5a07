#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
網路診斷工具
用於診斷PLC網路連接問題
"""

import socket
import subprocess
import sys
import time

def ping_host(host):
    """Ping測試"""
    print(f"Ping測試: {host}")
    try:
        # Windows ping命令
        result = subprocess.run(['ping', '-n', '4', host], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✓ Ping成功")
            # 提取延遲信息
            lines = result.stdout.split('\n')
            for line in lines:
                if 'ms' in line and '時間' in line:
                    print(f"  {line.strip()}")
            return True
        else:
            print("✗ Ping失敗")
            print(f"  錯誤: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("✗ Ping超時")
        return False
    except Exception as e:
        print(f"✗ Ping錯誤: {e}")
        return False

def test_port(host, port):
    """端口連接測試"""
    print(f"\n端口測試: {host}:{port}")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print("✓ 端口可達")
            return True
        else:
            print("✗ 端口不可達")
            print(f"  錯誤碼: {result}")
            return False
    except Exception as e:
        print(f"✗ 端口測試錯誤: {e}")
        return False

def scan_common_ports(host):
    """掃描常見PLC端口"""
    print(f"\n掃描常見PLC端口: {host}")
    common_ports = [
        (502, "Modbus TCP"),
        (503, "Modbus TCP備用"),
        (102, "S7通信"),
        (2404, "IEC 61850"),
        (44818, "OPC UA"),
        (80, "HTTP Web界面"),
        (23, "Telnet"),
        (21, "FTP"),
    ]
    
    open_ports = []
    for port, desc in common_ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"  ✓ {port} ({desc}) - 開放")
                open_ports.append((port, desc))
            else:
                print(f"  ✗ {port} ({desc}) - 關閉")
        except:
            print(f"  ? {port} ({desc}) - 測試失敗")
    
    return open_ports

def get_local_network_info():
    """獲取本機網路信息"""
    print("\n本機網路信息:")
    try:
        # 獲取本機IP
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        print(f"  主機名: {hostname}")
        print(f"  本機IP: {local_ip}")
        
        # 檢查是否在同一網段
        plc_ip = "***********"
        local_parts = local_ip.split('.')
        plc_parts = plc_ip.split('.')
        
        if local_parts[:3] == plc_parts[:3]:
            print(f"  ✓ 與PLC在同一網段 ({'.'.join(local_parts[:3])}.x)")
        else:
            print(f"  ⚠ 與PLC不在同一網段")
            print(f"    本機網段: {'.'.join(local_parts[:3])}.x")
            print(f"    PLC網段: {'.'.join(plc_parts[:3])}.x")
            
    except Exception as e:
        print(f"  錯誤: {e}")

def suggest_solutions():
    """建議解決方案"""
    print("\n" + "="*60)
    print("問題排查建議")
    print("="*60)
    print("1. 檢查PLC電源和網路連接")
    print("   - 確認PLC已開機")
    print("   - 檢查網路線連接")
    print("   - 檢查網路指示燈")
    
    print("\n2. 檢查IP地址設定")
    print("   - 確認PLC IP地址是否為 ***********")
    print("   - 檢查子網掩碼設定")
    print("   - 確認本機與PLC在同一網段")
    
    print("\n3. 檢查PLC Modbus設定")
    print("   - 確認Modbus TCP服務已啟用")
    print("   - 檢查端口設定 (預設502)")
    print("   - 確認站號設定")
    
    print("\n4. 檢查防火牆設定")
    print("   - Windows防火牆")
    print("   - 企業防火牆")
    print("   - PLC內建防火牆")
    
    print("\n5. 嘗試其他工具")
    print("   - 使用PLC廠商提供的軟體測試")
    print("   - 使用Modbus測試工具")
    print("   - 檢查PLC手冊中的網路設定")

def main():
    """主程式"""
    plc_ip = "***********"
    modbus_port = 502
    
    print("PLC網路診斷工具")
    print(f"目標: {plc_ip}:{modbus_port}")
    print("="*60)
    
    # 1. 獲取本機網路信息
    get_local_network_info()
    
    # 2. Ping測試
    print("\n" + "="*60)
    ping_success = ping_host(plc_ip)
    
    # 3. 端口測試
    port_success = test_port(plc_ip, modbus_port)
    
    # 4. 掃描其他端口
    open_ports = scan_common_ports(plc_ip)
    
    # 5. 結果分析
    print("\n" + "="*60)
    print("診斷結果")
    print("="*60)
    
    if ping_success and port_success:
        print("✓ 網路連接正常，PLC Modbus服務可用")
        print("  問題可能在於:")
        print("  - Modbus通信參數設定")
        print("  - 地址映射問題")
        print("  - PLC程式邏輯")
    elif ping_success and not port_success:
        print("⚠ 可以Ping通PLC，但Modbus端口不可達")
        print("  問題可能在於:")
        print("  - Modbus TCP服務未啟用")
        print("  - 端口設定錯誤")
        print("  - PLC防火牆阻擋")
        if open_ports:
            print(f"  發現開放端口: {[f'{p}({d})' for p, d in open_ports]}")
    elif not ping_success:
        print("✗ 無法Ping通PLC")
        print("  問題可能在於:")
        print("  - PLC未開機或網路未連接")
        print("  - IP地址錯誤")
        print("  - 網路路由問題")
        print("  - 防火牆阻擋ICMP")
    
    # 6. 建議解決方案
    suggest_solutions()

if __name__ == "__main__":
    main()
