#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PLC M101診斷工具
專門用於診斷M101狀態讀取問題
"""

import socket
import struct
import time
import sys

class PLCDiagnostic:
    def __init__(self, host='***********', port=502):
        self.host = host
        self.port = port
        self.sock = None
        self.transaction_id = 0
        
    def test_connection(self):
        """測試網路連接"""
        print("="*60)
        print("1. 網路連接測試")
        print("="*60)
        
        try:
            # 測試ping (使用socket)
            test_sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            test_sock.settimeout(5)
            result = test_sock.connect_ex((self.host, self.port))
            test_sock.close()
            
            if result == 0:
                print(f"✓ 可以連接到 {self.host}:{self.port}")
                return True
            else:
                print(f"✗ 無法連接到 {self.host}:{self.port}")
                print("  可能原因:")
                print("  - PLC未開機或網路未連接")
                print("  - IP地址錯誤")
                print("  - Modbus TCP服務未啟用")
                return False
                
        except Exception as e:
            print(f"✗ 連接測試失敗: {e}")
            return False
    
    def connect(self):
        """建立Modbus連接"""
        try:
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.settimeout(10)
            self.sock.connect((self.host, self.port))
            return True
        except Exception as e:
            print(f"Modbus連接失敗: {e}")
            return False
    
    def test_modbus_communication(self):
        """測試Modbus通信"""
        print("\n" + "="*60)
        print("2. Modbus通信測試")
        print("="*60)
        
        if not self.connect():
            return False
            
        # 測試讀取功能碼1 (Read Coils)
        test_cases = [
            (0, 1, "測試地址0"),
            (1, 1, "測試地址1"), 
            (100, 1, "測試地址100 (M100)"),
            (101, 1, "測試地址101 (M101)"),
            (100, 10, "測試地址100-109"),
        ]
        
        for addr, count, desc in test_cases:
            print(f"\n{desc}:")
            result = self._read_coils_raw(addr, count)
            if result:
                print(f"  ✓ 成功讀取")
                coils = self._parse_coils(result, count)
                if coils:
                    for i, coil in enumerate(coils):
                        print(f"    地址{addr+i}: {'ON' if coil else 'OFF'}")
            else:
                print(f"  ✗ 讀取失敗")
        
        self.sock.close()
        return True
    
    def _read_coils_raw(self, start_address, count):
        """原始線圈讀取"""
        try:
            self.transaction_id += 1
            transaction_id = self.transaction_id
            protocol_id = 0
            unit_id = 1
            function_code = 1
            
            pdu = struct.pack('>BHH', function_code, start_address, count)
            length = len(pdu) + 1
            adu = struct.pack('>HHHB', transaction_id, protocol_id, length, unit_id) + pdu
            
            self.sock.send(adu)
            response = self.sock.recv(1024)
            
            if len(response) < 9:
                return None
                
            resp_func_code = response[7]
            if resp_func_code == (function_code + 0x80):
                error_code = response[8] if len(response) > 8 else 0
                print(f"    Modbus錯誤: {error_code}")
                return None
            elif resp_func_code != function_code:
                print(f"    功能碼錯誤: {resp_func_code}")
                return None
                
            return response
            
        except Exception as e:
            print(f"    讀取異常: {e}")
            return None
    
    def _parse_coils(self, response, count):
        """解析線圈數據"""
        try:
            byte_count = response[8]
            coil_data = response[9:9+byte_count]
            
            coils = []
            for byte_val in coil_data:
                for bit in range(8):
                    if len(coils) < count:
                        coils.append(bool(byte_val & (1 << bit)))
            
            return coils[:count]
        except:
            return None
    
    def test_address_mapping(self):
        """測試地址映射"""
        print("\n" + "="*60)
        print("3. M101地址映射測試")
        print("="*60)
        
        if not self.connect():
            return False
        
        print("匯川PLC的M寄存器可能有不同的Modbus地址映射:")
        
        # 常見的地址映射方式
        mappings = [
            (101, "直接映射: M101 → Modbus地址101"),
            (100, "偏移映射: M101 → 從M100開始的第2個位"),
            (1, "零基映射: M101 → Modbus地址1"),
            (8193, "標準映射: M101 → 8192+101"),  # 有些PLC使用8192作為M區起始
            (10101, "擴展映射: M101 → 10000+101"),  # 有些使用10000作為起始
        ]
        
        for addr, desc in mappings:
            print(f"\n{desc}:")
            if addr == 100:
                # 特殊處理：讀取多個然後取第二個
                result = self._read_coils_raw(addr, 5)
                if result:
                    coils = self._parse_coils(result, 5)
                    if coils and len(coils) > 1:
                        print(f"  M101狀態: {'ON' if coils[1] else 'OFF'}")
                        print("  周圍狀態:")
                        for i, coil in enumerate(coils):
                            print(f"    M{100+i}: {'ON' if coil else 'OFF'}")
                    else:
                        print("  ✗ 解析失敗")
                else:
                    print("  ✗ 讀取失敗")
            else:
                result = self._read_coils_raw(addr, 1)
                if result:
                    coils = self._parse_coils(result, 1)
                    if coils:
                        print(f"  狀態: {'ON' if coils[0] else 'OFF'}")
                    else:
                        print("  ✗ 解析失敗")
                else:
                    print("  ✗ 讀取失敗")
        
        self.sock.close()
        return True
    
    def run_full_diagnostic(self):
        """運行完整診斷"""
        print("PLC M101狀態診斷工具")
        print("目標PLC: {}:{}".format(self.host, self.port))
        
        # 1. 網路連接測試
        if not self.test_connection():
            print("\n診斷結論: 網路連接問題，請檢查PLC網路設定")
            return
        
        # 2. Modbus通信測試
        if not self.test_modbus_communication():
            print("\n診斷結論: Modbus通信問題")
            return
        
        # 3. 地址映射測試
        self.test_address_mapping()
        
        print("\n" + "="*60)
        print("診斷完成")
        print("="*60)
        print("如果所有測試都失敗，請檢查:")
        print("1. PLC型號和Modbus配置")
        print("2. M寄存器是否存在於該PLC中")
        print("3. PLC程式是否正確設定M101")

def main():
    """主程式"""
    diagnostic = PLCDiagnostic()
    diagnostic.run_full_diagnostic()

if __name__ == "__main__":
    main()
